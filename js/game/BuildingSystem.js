// 王国争霸H5 - 建筑系统

class Building {
    constructor(type, x, y) {
        this.id = Utils.generateId();
        this.type = type;
        this.x = x;
        this.y = y;
        this.level = 1;
        this.isBuilding = false;
        this.buildTimeRemaining = 0;
        this.lastProduction = Date.now();
        
        // 从游戏数据获取建筑信息
        this.data = GameData.buildings[type];
        if (!this.data) {
            throw new Error(`Unknown building type: ${type}`);
        }
        
        this.width = 80;
        this.height = 80;
    }

    // 开始建造
    startBuilding() {
        this.isBuilding = true;
        this.buildTimeRemaining = this.data.buildTime * 1000; // 转换为毫秒
        
        GameEvents.emit(GAME_EVENTS.BUILDING_BUILD, this);
    }

    // 更新建筑状态
    update(deltaTime) {
        if (this.isBuilding) {
            this.buildTimeRemaining -= deltaTime;
            
            if (this.buildTimeRemaining <= 0) {
                this.completeBuild();
            }
        }
    }

    // 完成建造
    completeBuild() {
        this.isBuilding = false;
        this.buildTimeRemaining = 0;

        // 给予基础经验奖励
        const baseExp = Math.floor(this.data.buildTime / 10) + 5; // 建造时间越长经验越多
        if (window.Game && window.Game.gainExp) {
            window.Game.gainExp(baseExp);
        }

        GameEvents.emit(GAME_EVENTS.BUILDING_COMPLETE, this);
        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.SUCCESS,
            text: `${this.data.name}建造完成！获得${baseExp}经验`
        });
    }

    // 升级建筑
    upgrade() {
        if (this.level >= this.data.maxLevel) {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: '建筑已达到最高等级！'
            });
            return false;
        }

        const upgradeCost = this.getUpgradeCost();
        if (!window.Game.resources.hasResources(upgradeCost)) {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.ERROR,
                text: '资源不足，无法升级！'
            });
            return false;
        }

        window.Game.resources.spendResources(upgradeCost);
        this.level++;
        
        GameEvents.emit(GAME_EVENTS.BUILDING_UPGRADE, this);
        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.SUCCESS,
            text: `${this.data.name}升级到${this.level}级！`
        });
        
        return true;
    }

    // 获取升级成本
    getUpgradeCost() {
        const baseCost = this.data.cost;
        const multiplier = Math.pow(1.5, this.level - 1);
        
        const upgradeCost = {};
        for (const [resource, amount] of Object.entries(baseCost)) {
            upgradeCost[resource] = Math.floor(amount * multiplier);
        }
        
        return upgradeCost;
    }

    // 获取生产量
    getProduction() {
        if (!this.data.produces || this.isBuilding) {
            return {};
        }
        
        const production = {};
        const levelMultiplier = 1 + (this.level - 1) * 0.2;
        
        for (const [resource, amount] of Object.entries(this.data.produces)) {
            production[resource] = amount * levelMultiplier;
        }
        
        return production;
    }

    // 渲染建筑
    render(ctx) {
        // 建筑主体
        if (this.isBuilding) {
            // 建造中的样式
            ctx.fillStyle = 'rgba(200, 200, 200, 0.5)';
            ctx.strokeStyle = '#666';
            ctx.setLineDash([5, 5]);
        } else {
            // 完成的建筑样式
            ctx.fillStyle = '#8B4513';
            ctx.strokeStyle = '#654321';
            ctx.setLineDash([]);
        }
        
        ctx.fillRect(this.x, this.y, this.width, this.height);
        ctx.strokeRect(this.x, this.y, this.width, this.height);
        
        // 建筑图标
        ctx.font = '32px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = this.isBuilding ? '#999' : '#333';
        ctx.fillText(
            this.data.icon,
            this.x + this.width / 2,
            this.y + this.height / 2
        );
        
        // 等级显示
        if (!this.isBuilding && this.level > 1) {
            ctx.font = '12px Arial';
            ctx.fillStyle = '#FFD700';
            ctx.fillText(
                `Lv.${this.level}`,
                this.x + this.width / 2,
                this.y + this.height - 10
            );
        }
        
        // 建造进度
        if (this.isBuilding) {
            const progress = 1 - (this.buildTimeRemaining / (this.data.buildTime * 1000));
            const barWidth = this.width - 10;
            const barHeight = 6;
            const barX = this.x + 5;
            const barY = this.y - 15;
            
            // 进度条背景
            ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            ctx.fillRect(barX, barY, barWidth, barHeight);
            
            // 进度条
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(barX, barY, barWidth * progress, barHeight);
        }
    }

    // 检查点击
    isPointInside(x, y) {
        return x >= this.x && x <= this.x + this.width &&
               y >= this.y && y <= this.y + this.height;
    }

    // 获取建筑信息
    getInfo() {
        return {
            id: this.id,
            type: this.type,
            name: this.data.name,
            level: this.level,
            maxLevel: this.data.maxLevel,
            isBuilding: this.isBuilding,
            buildTimeRemaining: this.buildTimeRemaining,
            production: this.getProduction(),
            upgradeCost: this.getUpgradeCost()
        };
    }
}

class BuildingSystem {
    constructor() {
        this.buildings = new Map();
        this.selectedBuilding = null;
        this.buildMode = false;
        this.buildingType = null;
        
        this.init();
    }

    // 初始化建筑系统
    init() {
        // 监听画布点击事件
        GameEvents.on('canvas_click', this.handleCanvasClick.bind(this));
        
        // 监听建筑选择事件
        GameEvents.on(GAME_EVENTS.BUILDING_SELECT, this.selectBuilding.bind(this));
        
        console.log('Building system initialized');
    }

    // 更新建筑系统
    update(deltaTime) {
        for (const building of this.buildings.values()) {
            building.update(deltaTime);
        }
    }

    // 建造建筑
    buildBuilding(type, x, y) {
        console.log(`Building check started for ${type} at (${x}, ${y})`);

        const buildingData = GameData.buildings[type];
        if (!buildingData) {
            console.error(`Unknown building type: ${type}`);
            return false;
        }
        console.log('Building data found:', buildingData);

        // 检查等级要求
        const playerLevel = window.Game.player.level;
        const requiredLevel = buildingData.unlockLevel;
        console.log(`Level check: player=${playerLevel}, required=${requiredLevel}`);

        if (playerLevel < requiredLevel) {
            console.log('Level check failed');
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: `需要等级${requiredLevel}才能建造${buildingData.name}！`
            });
            return false;
        }
        console.log('Level check passed');

        // 检查资源
        const hasResources = window.Game.resources.hasResources(buildingData.cost);
        console.log('Resource check:', hasResources);
        console.log('Required resources:', buildingData.cost);
        console.log('Current resources:', window.Game.resources.resources);

        if (!hasResources) {
            console.log('Resource check failed');
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.ERROR,
                text: '资源不足，无法建造！'
            });
            return false;
        }
        console.log('Resource check passed');

        // 检查位置是否可用
        const positionValid = this.isPositionValid(x, y);
        console.log('Position check:', positionValid);

        if (!positionValid) {
            console.log('Position check failed');
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: '此位置无法建造建筑！'
            });
            return false;
        }
        console.log('Position check passed');

        // 消耗资源
        window.Game.resources.spendResources(buildingData.cost);

        // 创建建筑
        const building = new Building(type, x, y);
        building.startBuilding();
        
        this.buildings.set(building.id, building);
        
        // 添加到渲染层
        if (window.Game.engine) {
            window.Game.engine.addToLayer('buildings', building);
        }

        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.INFO,
            text: `开始建造${buildingData.name}...`
        });

        return true;
    }

    // 检查位置是否有效
    isPositionValid(x, y, width = 80, height = 80) {
        console.log(`Position validation for (${x}, ${y}) with size ${width}x${height}`);

        // 检查边界，留出一些边距
        const margin = 10;
        console.log(`Checking boundaries with margin ${margin}`);

        if (x < margin || y < margin) {
            console.log(`Position too close to top/left edge: x=${x}, y=${y}, margin=${margin}`);
            return false;
        }

        const canvasSize = window.Game.engine.getCanvasSize();
        console.log('Canvas size:', canvasSize);

        const rightEdge = x + width;
        const bottomEdge = y + height;
        const maxRight = canvasSize.width - margin;
        const maxBottom = canvasSize.height - margin;

        console.log(`Edge check: right=${rightEdge} vs max=${maxRight}, bottom=${bottomEdge} vs max=${maxBottom}`);

        if (rightEdge > maxRight || bottomEdge > maxBottom) {
            console.log(`Position too close to right/bottom edge`);
            return false;
        }

        // 检查与其他建筑的重叠，增加安全距离
        const safeDistance = 10;
        const buildingCount = this.buildings.size;
        console.log(`Checking overlap with ${buildingCount} existing buildings, safe distance=${safeDistance}`);

        for (const building of this.buildings.values()) {
            const overlapping = this.isOverlapping(
                x - safeDistance,
                y - safeDistance,
                width + safeDistance * 2,
                height + safeDistance * 2,
                building
            );

            if (overlapping) {
                console.log(`Overlapping with building at (${building.x}, ${building.y})`);
                return false;
            }
        }

        console.log('Position validation passed');
        return true;
    }

    // 检查两个矩形是否重叠
    isOverlapping(x1, y1, w1, h1, building) {
        const x2 = building.x;
        const y2 = building.y;
        const w2 = building.width;
        const h2 = building.height;

        return !(x1 + w1 <= x2 || x2 + w2 <= x1 || y1 + h1 <= y2 || y2 + h2 <= y1);
    }

    // 处理画布点击
    handleCanvasClick(event) {
        const { x, y } = event;
        console.log('Canvas clicked at:', x, y, 'buildMode:', this.buildMode, 'buildingType:', this.buildingType);

        if (this.buildMode && this.buildingType) {
            console.log('Attempting to build building at:', x - 40, y - 40);
            // 建造模式
            const success = this.buildBuilding(this.buildingType, x - 40, y - 40);
            console.log('Building result:', success);
            this.exitBuildMode();
        } else {
            console.log('Not in build mode or no building type selected');
        }
    }

    // 选择建筑
    selectBuilding(building) {
        this.selectedBuilding = building;
        this.showBuildingInfo(building);
    }

    // 显示建筑信息
    showBuildingInfo(building) {
        const infoPanel = Utils.$('#buildingInfo');
        const nameElement = Utils.$('#buildingName');
        const descElement = Utils.$('#buildingDesc');
        const actionsElement = Utils.$('#buildingActions');

        if (!infoPanel || !nameElement || !descElement || !actionsElement) {
            return;
        }

        // 显示建筑信息
        Utils.setContent(nameElement, `${building.data.name} (等级 ${building.level})`);
        Utils.setContent(descElement, building.data.description);

        // 清空操作按钮
        actionsElement.innerHTML = '';

        if (!building.isBuilding) {
            // 升级按钮
            if (building.level < building.data.maxLevel) {
                const upgradeBtn = Utils.createElement('button', 'btn btn-primary', '升级');
                upgradeBtn.onclick = () => {
                    building.upgrade();
                    this.showBuildingInfo(building); // 刷新信息
                };
                actionsElement.appendChild(upgradeBtn);
            }

            // 拆除按钮
            const demolishBtn = Utils.createElement('button', 'btn btn-secondary', '拆除');
            demolishBtn.onclick = () => {
                this.demolishBuilding(building.id);
                Utils.hide(infoPanel);
            };
            actionsElement.appendChild(demolishBtn);
        }

        Utils.show(infoPanel);
    }

    // 拆除建筑
    demolishBuilding(buildingId) {
        const building = this.buildings.get(buildingId);
        if (!building) return false;

        // 从渲染层移除
        if (window.Game.engine) {
            window.Game.engine.removeFromLayer('buildings', building);
        }

        // 从建筑列表移除
        this.buildings.delete(buildingId);

        // 返还部分资源
        const refund = {};
        for (const [resource, amount] of Object.entries(building.data.cost)) {
            refund[resource] = Math.floor(amount * 0.5); // 返还50%
        }
        
        for (const [resource, amount] of Object.entries(refund)) {
            window.Game.resources.addResource(resource, amount);
        }

        GameEvents.emit(GAME_EVENTS.BUILDING_DESTROY, building);
        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.INFO,
            text: `${building.data.name}已拆除`
        });

        return true;
    }

    // 进入建造模式
    enterBuildMode(buildingType) {
        try {
            console.log('Entering build mode for:', buildingType);

            this.buildMode = true;
            this.buildingType = buildingType;
            console.log('Build mode flags set');

            // 改变鼠标样式
            if (window.Game.engine && window.Game.engine.canvas) {
                window.Game.engine.canvas.style.cursor = 'crosshair';
                console.log('Cursor changed to crosshair');
            } else {
                console.error('Canvas not available for cursor change');
            }

            // 添加鼠标移动监听，显示建筑预览
            console.log('Setting up build preview...');
            this.setupBuildPreview();
            console.log('Build preview setup complete');

            // 检查建筑数据
            const buildingData = GameData.buildings[buildingType];
            if (!buildingData) {
                console.error('Building data not found for:', buildingType);
                return;
            }
            console.log('Building data found:', buildingData);

            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.INFO,
                text: `选择位置建造${buildingData.name}`
            });
            console.log('UI message emitted');

            console.log('Build mode entered successfully. buildMode:', this.buildMode, 'buildingType:', this.buildingType);
            return true;
        } catch (error) {
            console.error('Error in enterBuildMode:', error);
            console.error('Error stack:', error.stack);
            return false;
        }
    }

    // 退出建造模式
    exitBuildMode() {
        this.buildMode = false;
        this.buildingType = null;
        this.previewBuilding = null;

        // 恢复鼠标样式
        if (window.Game.engine && window.Game.engine.canvas) {
            window.Game.engine.canvas.style.cursor = 'pointer';
        }

        // 移除预览监听
        this.removeBuildPreview();
    }

    // 设置建筑预览
    setupBuildPreview() {
        try {
            console.log('Creating preview building object...');
            this.previewBuilding = {
                type: this.buildingType,
                x: 0,
                y: 0,
                width: 80,
                height: 80,
                valid: false
            };
            console.log('Preview building object created:', this.previewBuilding);

            // 添加鼠标移动监听
            if (window.Game.engine && window.Game.engine.canvas) {
                console.log('Adding mouse move handler...');
                this.mouseMoveHandler = (e) => {
                    try {
                        const rect = window.Game.engine.canvas.getBoundingClientRect();
                        const x = e.clientX - rect.left - 40; // 居中
                        const y = e.clientY - rect.top - 40;

                        this.previewBuilding.x = x;
                        this.previewBuilding.y = y;
                        this.previewBuilding.valid = this.isPositionValid(x, y);
                    } catch (error) {
                        console.error('Error in mouse move handler:', error);
                    }
                };

                window.Game.engine.canvas.addEventListener('mousemove', this.mouseMoveHandler);
                console.log('Mouse move handler added successfully');
            } else {
                console.error('Canvas not available for mouse move handler');
            }
        } catch (error) {
            console.error('Error in setupBuildPreview:', error);
        }
    }

    // 移除建筑预览
    removeBuildPreview() {
        if (this.mouseMoveHandler && window.Game.engine && window.Game.engine.canvas) {
            window.Game.engine.canvas.removeEventListener('mousemove', this.mouseMoveHandler);
            this.mouseMoveHandler = null;
        }
    }

    // 获取指定位置的建筑
    getBuildingAt(x, y) {
        for (const building of this.buildings.values()) {
            if (building.isPointInside(x, y)) {
                return building;
            }
        }
        return null;
    }

    // 获取所有建筑
    getAllBuildings() {
        return Array.from(this.buildings.values());
    }

    // 获取指定类型的建筑数量
    getBuildingCount(type) {
        let count = 0;
        for (const building of this.buildings.values()) {
            if (building.type === type && !building.isBuilding) {
                count++;
            }
        }
        return count;
    }

    // 重置建筑系统
    reset() {
        // 清空所有建筑
        if (window.Game.engine) {
            for (const building of this.buildings.values()) {
                window.Game.engine.removeFromLayer('buildings', building);
            }
        }
        
        this.buildings.clear();
        this.selectedBuilding = null;
        this.exitBuildMode();
    }

    // 加载建筑数据
    loadData(buildingsData) {
        this.reset();
        
        for (const data of buildingsData) {
            const building = new Building(data.type, data.x, data.y);
            building.level = data.level;
            building.isBuilding = data.isBuilding || false;
            building.buildTimeRemaining = data.buildTimeRemaining || 0;
            
            this.buildings.set(building.id, building);
            
            if (window.Game.engine) {
                window.Game.engine.addToLayer('buildings', building);
            }
        }
    }

    // 获取保存数据
    getSaveData() {
        const buildingsData = [];
        
        for (const building of this.buildings.values()) {
            buildingsData.push({
                type: building.type,
                x: building.x,
                y: building.y,
                level: building.level,
                isBuilding: building.isBuilding,
                buildTimeRemaining: building.buildTimeRemaining
            });
        }
        
        return buildingsData;
    }
}

// 导出建筑系统
window.Building = Building;
window.BuildingSystem = BuildingSystem;
